# This workflow is triggered by a comment on PR with the text ".bundle"
# It bundles the ARM64 Desktop App, then creates a PR comment with a link to download the app.

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number to comment on'
        required: true
        type: string

# permissions needed for reacting to IssueOps commands on PRs
permissions:
  pull-requests: write
  checks: read

name: Bundle ARM64 Desktop App

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.issue && github.event.issue.number) || github.event.inputs.pr_number }}
  cancel-in-progress: true

jobs:
  trigger-on-command:
    if: >
      github.event_name == 'workflow_dispatch' ||
      (github.event.issue.pull_request && contains(github.event.comment.body, '.bundle'))
    name: Trigger on ".bundle" PR comment
    runs-on: ubuntu-latest
    outputs:
      continue: 'true'
      pr_number: ${{ steps.command.outputs.issue_number || github.event.inputs.pr_number }}
      pr_sha: ${{ steps.get_pr_info.outputs.sha }}
    steps:
      - name: Debug workflow trigger
        env:
          WORKFLOW_NAME: ${{ github.workflow }}
          WORKFLOW_REF: ${{ github.ref }}
          EVENT_NAME: ${{ github.event_name }}
          EVENT_ACTION: ${{ github.event.action }}
          ACTOR: ${{ github.actor }}
          REPOSITORY: ${{ github.repository }}
        run: |
          echo "=== Workflow Trigger Info ==="
          echo "Workflow: ${WORKFLOW_NAME}"
          echo "Ref: ${WORKFLOW_REF}"
          echo "Event: ${EVENT_NAME}"
          echo "Action: ${EVENT_ACTION}"
          echo "Actor: ${ACTOR}"
          echo "Repository: ${REPOSITORY}"

      - name: Run command action
        uses: github/command@319d5236cc34ed2cb72a47c058a363db0b628ebe # pin@v1.3.0
        id: command
        with:
          command: ".bundle"
          skip_reviews: true
          reaction: "eyes"
          allowed_contexts: pull_request

      # Get the PR's SHA
      - name: Get PR info
        id: get_pr_info
        if: ${{ steps.command.outputs.continue == 'true' || github.event_name == 'workflow_dispatch' }}
        uses: actions/github-script@v7
        with:
          script: |
            let prNumber;
            
            if (context.eventName === 'workflow_dispatch') {
              prNumber = context.payload.inputs.pr_number;
            } else {
              prNumber = context.payload.issue.number;
            }
            
            if (!prNumber) {
              throw new Error('No PR number found');
            }
            
            console.log('Using PR number:', prNumber);
            
            const { data: pr } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: parseInt(prNumber, 10)
            });
            
            console.log('PR Details:', {
              number: pr.number,
              head: {
                ref: pr.head.ref,
                sha: pr.head.sha,
                label: pr.head.label
              },
              base: {
                ref: pr.base.ref,
                sha: pr.base.sha,
                label: pr.base.label
              }
            });
            
            core.setOutput('sha', pr.head.sha);

  bundle-desktop:
    needs: [trigger-on-command]
    if: ${{ needs.trigger-on-command.outputs.continue == 'true' }}
    uses: ./.github/workflows/bundle-desktop.yml
    permissions:
      id-token: write
      contents: read
    with:
      signing: false
      ref: ${{ needs.trigger-on-command.outputs.pr_sha }}

  pr-comment-arm64:
    name: PR Comment with macOS ARM64 App
    runs-on: ubuntu-latest
    needs: [trigger-on-command, bundle-desktop]
    permissions:
      pull-requests: write

    steps:
      - name: Download ARM64 artifact
        uses: actions/download-artifact@cc203385981b70ca67e1cc392babf9cc229d5806 # pin@v4
        with:
          name: Goose-darwin-arm64
          path: arm64-dist

      - name: Comment on PR with ARM64 download link
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # pin@v4
        with:
          issue-number: ${{ needs.trigger-on-command.outputs.pr_number }}
          body: |
            ### macOS ARM64 Desktop App (Apple Silicon)

            [📱 Download macOS Desktop App (arm64, signed)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/Goose-darwin-arm64.zip)

            **Instructions:**
            After downloading, unzip the file and drag the Goose.app to your Applications folder. The app is signed and notarized for macOS.

            This link is provided by nightly.link and will work even if you're not logged into GitHub.