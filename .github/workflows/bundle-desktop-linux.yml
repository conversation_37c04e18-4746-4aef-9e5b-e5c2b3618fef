# This is a **reuseable** workflow that bundles the Desktop App for Linux.
# It doesn't get triggered on its own. It gets used in multiple workflows:
#  - release.yml
#  - canary.yml (when added)
#  - pr-comment-bundle-desktop.yml (when added)
on:
  workflow_call:
    inputs:
      version:
        description: 'Version to set for the build'
        required: false
        default: ""
        type: string
      ref:
        type: string
        required: false
        default: ''

name: "Bundle Desktop (Linux)"

jobs:
  build-desktop-linux:
    name: Build Desktop (Linux)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
        with:
          ref: ${{ inputs.ref }}
          fetch-depth: 0

      - name: Update versions
        if: ${{ inputs.version != '' }}
        run: |
          # Update version in Cargo.toml
          sed -i.bak 's/^version = ".*"/version = "'${{ inputs.version }}'"/' Cargo.toml
          rm -f Cargo.toml.bak

          # Update version in package.json
          cd ui/desktop
          npm version ${{ inputs.version }} --no-git-tag-version --allow-same-version

      - name: Debug workflow info
        env:
          WORKFLOW_NAME: ${{ github.workflow }}
          WORKFLOW_REF: ${{ github.ref }}
          EVENT_NAME: ${{ github.event_name }}
          REPOSITORY: ${{ github.repository }}
        run: |
          echo "=== Workflow Information ==="
          echo "Workflow: ${WORKFLOW_NAME}"
          echo "Ref: ${WORKFLOW_REF}"
          echo "Event: ${EVENT_NAME}"
          echo "Repo: ${REPOSITORY}"
          echo ""
          echo "=== System Information ==="
          uname -a
          lsb_release -a || true
          df -h

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            libnss3-dev \
            libatk-bridge2.0-dev \
            libdrm2 \
            libxcomposite1 \
            libxdamage1 \
            libxrandr2 \
            libgbm1 \
            libxss1 \
            libasound2t64 \
            rpm \
            fakeroot \
            dpkg-dev \
            protobuf-compiler

      - name: Pre-build cleanup
        run: |
          echo "Performing aggressive pre-build cleanup..."
          # Clean npm cache
          npm cache clean --force || true
          # Clean any previous build artifacts
          rm -rf target || true
          # Clean Homebrew cache (if exists)
          brew cleanup || true
          # Remove unnecessary large directories
          sudo rm -rf /usr/share/dotnet || true
          sudo rm -rf /usr/local/lib/android || true
          sudo rm -rf /opt/ghc || true
          sudo rm -rf /usr/local/share/boost || true
          # Clean apt cache
          sudo apt-get clean || true
          sudo apt-get autoremove -y || true
          # Check disk space after cleanup
          df -h

      - name: Activate hermit and set CARGO_HOME
        run: |
          source bin/activate-hermit
          echo "CARGO_HOME=$CARGO_HOME" >> $GITHUB_ENV
          echo "RUSTUP_HOME=$RUSTUP_HOME" >> $GITHUB_ENV

      - name: Install cross
        run: source ./bin/activate-hermit && cargo install cross --git https://github.com/cross-rs/cross

      - name: Cache Cargo artifacts
        uses: actions/cache@2f8e54208210a422b2efd51efaa6bd6d7ca8920f # pin@v3
        with:
          path: |
            ${{ env.CARGO_HOME }}/bin/
            ${{ env.CARGO_HOME }}/registry/index/
            ${{ env.CARGO_HOME }}/registry/cache/
            ${{ env.CARGO_HOME }}/git/db/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-

      - name: Set up Go
        uses: actions/setup-go@0a12ed9d6a96ab950c8f026ed9f722fe0da7ef32 # pin@v5
        with:
          go-version: '1.21'

      - name: Build temporal-service
        run: |
          echo "Building temporal-service using build.sh script..."
          cd temporal-service
          ./build.sh
          echo "temporal-service built successfully"

      - name: Build goosed binary
        env:
          CROSS_NO_WARNINGS: 0
          RUST_LOG: debug
          RUST_BACKTRACE: 1
          CROSS_VERBOSE: 1
        run: |
          source ./bin/activate-hermit
          export TARGET="x86_64-unknown-linux-gnu"
          rustup target add "${TARGET}"
          cross build --release --target ${TARGET} -p goose-server

      - name: Copy binaries into Electron folder
        run: |
          echo "Copying binaries to ui/desktop/src/bin/"
          export TARGET="x86_64-unknown-linux-gnu"
          mkdir -p ui/desktop/src/bin
          cp target/$TARGET/release/goosed ui/desktop/src/bin/
          cp temporal-service/temporal-service ui/desktop/src/bin/
          chmod +x ui/desktop/src/bin/goosed
          chmod +x ui/desktop/src/bin/temporal-service
          ls -la ui/desktop/src/bin/

      - name: Install npm dependencies
        run: |
          source ./bin/activate-hermit
          cd ui/desktop
          npm cache clean --force || true
          npm install
          # Verify installation
          ls -la node_modules/.bin/ | head -5

      - name: Build Linux packages
        run: |
          source ./bin/activate-hermit
          cd ui/desktop
          echo "Building Linux packages (.deb and .rpm)..."
          
          # Build both .deb and .rpm packages
          npm run make -- --platform=linux --arch=x64
          
          echo "Build completed. Checking output..."
          ls -la out/
          find out/ -name "*.deb" -o -name "*.rpm" | head -10

      - name: List generated files
        run: |
          echo "=== All files in out/ directory ==="
          find ui/desktop/out/ -type f | head -20
          echo ""
          echo "=== Package files specifically ==="
          find ui/desktop/out/ -name "*.deb" -o -name "*.rpm"
          echo ""
          echo "=== File sizes ==="
          find ui/desktop/out/ -name "*.deb" -o -name "*.rpm" -exec ls -lh {} \;

      - name: Upload .deb package
        uses: actions/upload-artifact@v4
        with:
          name: Goose-linux-x64-deb
          path: ui/desktop/out/make/deb/x64/*.deb
          if-no-files-found: error

      - name: Upload .rpm package
        uses: actions/upload-artifact@v4
        with:
          name: Goose-linux-x64-rpm
          path: ui/desktop/out/make/rpm/x64/*.rpm
          if-no-files-found: error

      - name: Upload combined Linux packages
        uses: actions/upload-artifact@v4
        with:
          name: Goose-linux-x64
          path: |
            ui/desktop/out/make/deb/x64/*.deb
            ui/desktop/out/make/rpm/x64/*.rpm
          if-no-files-found: error
