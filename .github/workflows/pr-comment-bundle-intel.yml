# This workflow is triggered by a comment on PR with the text ".bundle-intel"
# It bundles the Intel Desktop App, then creates a PR comment with a link to download the app.

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number to comment on'
        required: true
        type: string

# permissions needed for reacting to IssueOps commands on PRs
permissions:
  pull-requests: write
  checks: read

name: Bundle Intel Desktop App

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.issue && github.event.issue.number) || github.event.inputs.pr_number }}
  cancel-in-progress: true

jobs:
  trigger-on-command:
    if: >
      github.event_name == 'workflow_dispatch' ||
      (github.event.issue.pull_request && contains(github.event.comment.body, '.bundle-intel'))
    name: Trigger on ".bundle-intel" PR comment
    runs-on: ubuntu-latest
    outputs:
      continue: 'true'
      # Cannot use github.event.pull_request.number since the trigger is 'issue_comment'
      pr_number: ${{ steps.command.outputs.issue_number || github.event.inputs.pr_number }}
      head_sha: ${{ steps.set_head_sha.outputs.head_sha || github.sha }}
    steps:
      - name: Run command action
        uses: github/command@319d5236cc34ed2cb72a47c058a363db0b628ebe # pin@v1.3.0
        id: command
        with:
          command: ".bundle-intel"
          skip_reviews: true
          reaction: "eyes"
          allowed_contexts: pull_request

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
        
      - name: Get PR head SHA with gh
        id: set_head_sha
        run: |
          echo "Get PR head SHA with gh"
          HEAD_SHA=$(gh pr view "$ISSUE_NUMBER" --json headRefOid -q .headRefOid)
          echo "head_sha=$HEAD_SHA" >> $GITHUB_OUTPUT
          echo "head_sha=$HEAD_SHA"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_NUMBER: ${{ steps.command.outputs.issue_number }}

  bundle-desktop-intel:
    # Only run this if ".bundle-intel" command is detected.
    needs: [trigger-on-command]
    if: ${{ needs.trigger-on-command.outputs.continue == 'true' }}
    uses: ./.github/workflows/bundle-desktop-intel.yml
    permissions:
      id-token: write
      contents: read
    with:
      signing: false
      ref: ${{ needs.trigger-on-command.outputs.head_sha }}

  pr-comment-intel:
    name: PR Comment with macOS Intel App
    runs-on: ubuntu-latest
    needs: [trigger-on-command, bundle-desktop-intel]
    permissions:
      pull-requests: write

    steps:
      - name: Download Intel artifact
        uses: actions/download-artifact@cc203385981b70ca67e1cc392babf9cc229d5806 # pin@v4
        with:
          name: Goose-darwin-x64
          path: intel-dist

      - name: Comment on PR with Intel download link
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # pin@v4
        with:
          issue-number: ${{ needs.trigger-on-command.outputs.pr_number }}
          body: |
            ### macOS Intel Desktop App (x64)

            [💻 Download macOS Desktop App (Intel x64, signed)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/Goose-darwin-x64.zip)

            **Instructions:**
            After downloading, unzip the file and drag the Goose.app to your Applications folder. The app is signed and notarized for macOS.

            This link is provided by nightly.link and will work even if you're not logged into GitHub.