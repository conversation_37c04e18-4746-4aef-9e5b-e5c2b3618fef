name: Documentation Site Preview

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - closed
    paths:
      - 'documentation/**'
  push:
    branches-ignore:
      - 'dependabot/**'

concurrency: preview-${{ github.ref }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the branch
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # pin@v3

      - name: Setup Node.js
        if: github.event.action != 'closed'
        uses: actions/setup-node@1a4442cacd436585916779262731d5b162bc6ec7 # pin@v3
        with:
          node-version: 20 

      - name: Install dependencies and build docs
        working-directory: ./documentation
        if: github.event.action != 'closed'
        env:
          INKEEP_API_KEY: ${{ secrets.INKEEP_API_KEY }}
          INKEEP_INTEGRATION_ID: ${{ secrets.INKEEP_INTEGRATION_ID }}
          INKEEP_ORG_ID: ${{ secrets.INKEEP_ORG_ID }}
          TARGET_PATH: "/goose/pr-preview/pr-${{ github.event.number }}/"
        run: |
          npm install
          npm run build

      - name: Deploy preview
        uses: rossjrw/pr-preview-action@df22037db54ab6ee34d3c1e2b8810ac040a530c6 # pin@v1
        if: ${{ github.event.pull_request.head.repo.full_name == 'block/goose' }}
        with:
          source-dir: documentation/build
