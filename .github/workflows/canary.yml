# This workflow is for canary releases, automatically triggered by push to main
# This workflow is identical to "release.yml" with these exceptions:
#  - Triggered by push to main
#  - Github Release tagged as "canary"
on:
  push:
    paths-ignore:
      - "documentation/**"
    branches:
      - main

name: Canary

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ------------------------------------
  # 1) Prepare Version
  # ------------------------------------
  prepare-version:
    name: Prepare Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.set-version.outputs.version }}
    steps:
      # checkout code so we can read the Cargo.toml
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
      - name: Generate a canary version
        id: set-version
        run: |
          # Extract the version from Cargo.toml
          SHORT_SHA=$(echo "${GITHUB_SHA}" | cut -c1-7)
          VERSION=$(grep '^version\s*=' Cargo.toml | head -n 1 | cut -d\" -f2)
          VERSION="${VERSION}-canary+${SHORT_SHA}"
          echo "version=$VERSION" >> $GITHUB_OUTPUT

  # ------------------------------------
  # 2) Build CLI for multiple OS/Arch
  # ------------------------------------
  build-cli:
    needs: [prepare-version]
    uses: ./.github/workflows/build-cli.yml
    with:
      version: ${{ needs.prepare-version.outputs.version }}

  # ------------------------------------
  # 3) Upload Install CLI Script (we only need to do this once)
  # ------------------------------------
  install-script:
    name: Upload Install Script
    runs-on: ubuntu-latest
    needs: [build-cli]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
      - uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # pin@v4
        with:
          name: download_cli.sh
          path: download_cli.sh

  # ------------------------------------------------------------
  # 4) Bundle Desktop App (macOS only) - builds goosed and Electron app
  # ------------------------------------------------------------
  bundle-desktop:
    needs: [prepare-version]
    uses: ./.github/workflows/bundle-desktop.yml
    permissions:
      id-token: write
      contents: read
    with:
      version: ${{ needs.prepare-version.outputs.version }}
      signing: false

  # ------------------------------------------------------------
  # 5) Bundle Desktop App (Linux) - builds goosed and Electron app
  # ------------------------------------------------------------
  bundle-desktop-linux:
    needs: [prepare-version]
    uses: ./.github/workflows/bundle-desktop-linux.yml
    with:
      version: ${{ needs.prepare-version.outputs.version }}

  # ------------------------------------------------------------
  # 6) Bundle Desktop App (Windows) - builds goosed and Electron app
  # ------------------------------------------------------------
  bundle-desktop-windows:
    needs: [prepare-version]
    uses: ./.github/workflows/bundle-desktop-windows.yml
    with:
      version: ${{ needs.prepare-version.outputs.version }}
      signing: false

  # ------------------------------------
  # 7) Create/Update GitHub Release
  # ------------------------------------
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [build-cli, install-script, bundle-desktop, bundle-desktop-linux, bundle-desktop-windows]
    permissions:
      contents: write

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@cc203385981b70ca67e1cc392babf9cc229d5806 # pin@v4
        with:
          merge-multiple: true

      # Create/update the canary release
      - name: Release canary
        uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # pin@v1
        with:
          tag: canary
          name: Canary
          token: ${{ secrets.GITHUB_TOKEN }}
          artifacts: |
            goose-*.tar.bz2
            goose-*.zip
            Goose*.zip
            *.deb
            *.rpm
            download_cli.sh
          allowUpdates: true
          omitBody: true
          omitPrereleaseDuringUpdate: true
