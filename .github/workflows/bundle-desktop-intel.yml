# This is a **reuseable** workflow that bundles the Desktop App for Intel macOS.
# It doesn't get triggered on its own. It gets used in multiple workflows:
#  - release.yml
#  - canary.yml
#  - pr-comment-bundle-desktop.yml
on:
  workflow_call:
    inputs:
      version:
        description: 'Version to set for the build'
        required: false
        default: ""
        type: string
      signing:
        description: 'Whether to perform signing and notarization'
        required: false
        default: false
        type: boolean
      quick_test:
        description: 'Whether to perform the quick launch test'
        required: false
        default: true
        type: boolean
      ref:
        type: string
        required: false
        default: ''
    secrets:
      OSX_CODESIGN_ROLE:
        required: false

name: Reusable workflow to bundle desktop app for Intel Mac

jobs:
  bundle-desktop-intel:
    runs-on: macos-latest
    name: Bundle Desktop App on Intel macOS
    permissions:
      id-token: write
      contents: read
    steps:
      # Check initial disk space
      - name: Check initial disk space
        run: df -h

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          # Only pass ref if it's explicitly set, otherwise let checkout action use its default behavior
          ref: ${{ inputs.ref != '' && inputs.ref || '' }}
          fetch-depth: 0

      # Update versions before build
      - name: Update versions
        if: ${{ inputs.version != '' }}
        run: |
          # Update version in Cargo.toml
          sed -i.bak 's/^version = ".*"/version = "'${{ inputs.version }}'"/' Cargo.toml
          rm -f Cargo.toml.bak

          # Update version in package.json
          source ./bin/activate-hermit
          cd ui/desktop
          npm version ${{ inputs.version }} --no-git-tag-version --allow-same-version

      # Pre-build cleanup to ensure enough disk space
      - name: Pre-build cleanup
        run: |
          source ./bin/activate-hermit
          echo "Performing pre-build cleanup..."
          # Clean npm cache
          npm cache clean --force || true
          # Clean any previous build artifacts
          rm -rf target || true
          # Clean Homebrew cache
          brew cleanup || true
          # Remove unnecessary large directories
          rm -rf ~/Library/Caches/* || true
          # Check disk space after cleanup
          df -h

      - name: Cache Cargo registry
        uses: actions/cache@2f8e54208210a422b2efd51efaa6bd6d7ca8920f # pin@v3
        with:
          path: ~/.cargo/registry
          key: ${{ runner.os }}-intel-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-intel-cargo-registry-

      - name: Cache Cargo index
        uses: actions/cache@2f8e54208210a422b2efd51efaa6bd6d7ca8920f # pin@v3
        with:
          path: ~/.cargo/index
          key: ${{ runner.os }}-intel-cargo-index
          restore-keys: |
            ${{ runner.os }}-intel-cargo-index

      - name: Cache Cargo build
        uses: actions/cache@2f8e54208210a422b2efd51efaa6bd6d7ca8920f # pin@v3
        with:
          path: target
          key: ${{ runner.os }}-intel-cargo-build-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-intel-cargo-build-

      # Install Go for building temporal-service
      - name: Set up Go
        uses: actions/setup-go@0a12ed9d6a96ab950c8f026ed9f722fe0da7ef32 # pin@v5
        with:
          go-version: '1.21'

      - name: Build goose-server for Intel macOS (x86_64)
        run: |
          source ./bin/activate-hermit
          rustup target add x86_64-apple-darwin
          cargo build --release -p goose-server --target x86_64-apple-darwin

      # Build temporal-service using build.sh script
      - name: Build temporal-service
        run: |
          echo "Building temporal-service using build.sh script..."
          cd temporal-service
          ./build.sh
          echo "temporal-service built successfully"

      # Install and prepare temporal CLI
      - name: Install temporal CLI via hermit
        run: |
          echo "Installing temporal CLI via hermit..."
          ./bin/hermit install temporal-cli
          echo "temporal CLI installed successfully"

      # Post-build cleanup to free space
      - name: Post-build cleanup
        run: |
          echo "Performing post-build cleanup..."
          # Remove debug artifacts
          rm -rf target/debug || true
          rm -rf target/x86_64-apple-darwin/debug || true
          # Keep only what's needed for the next steps
          rm -rf target/x86_64-apple-darwin/release/deps || true
          rm -rf target/x86_64-apple-darwin/release/build || true
          rm -rf target/x86_64-apple-darwin/release/incremental || true
          # Check disk space after cleanup
          df -h

      - name: Copy binaries into Electron folder
        run: |
          cp target/x86_64-apple-darwin/release/goosed ui/desktop/src/bin/goosed
          cp temporal-service/temporal-service ui/desktop/src/bin/temporal-service
          cp bin/temporal ui/desktop/src/bin/temporal

      - name: Install dependencies
        run: source ../../bin/activate-hermit && npm ci
        working-directory: ui/desktop

      # Configure Electron builder for Intel architecture
      - name: Configure for Intel build
        run: |
          # Set the architecture to x64 for Intel Mac build
          jq '.build.mac.target[0].arch = "x64"' package.json > package.json.tmp && mv package.json.tmp package.json
        working-directory: ui/desktop

      # Check disk space before bundling
      - name: Check disk space before bundling
        run: df -h

      - name: Build App
        run: |
          source ../../bin/activate-hermit
          attempt=0
          max_attempts=2
          until [ $attempt -ge $max_attempts ]; do
            npm run bundle:intel && break
            attempt=$((attempt + 1))
            echo "Attempt $attempt failed. Retrying..."
            sleep 5
          done
          if [ $attempt -ge $max_attempts ]; then
            echo "Action failed after $max_attempts attempts."
            exit 1
          fi
        working-directory: ui/desktop

      - name: Configure AWS credentials
        if: ${{ inputs.signing }}
        uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4
        with:
          role-to-assume: "${{ secrets.OSX_CODESIGN_ROLE }}"
          aws-region: us-west-2

      - name: Codesigning and Notarization
        if: ${{ inputs.signing }}
        run: |
          set -e

          echo "⬆️ uploading unsigned app"
          source_job_url="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          unsigned_url="s3://block-goose-artifacts-bucket-production/unsigned/goose-${GITHUB_SHA}-${{ github.run_id }}-intel.zip"

          zip -q -u -r out/Goose-darwin-x64/Goose_intel_mac.zip entitlements.plist

          # upload unsigned goose to transfer bucket so it can be passed to lambda
          aws s3 cp --quiet out/Goose-darwin-x64/Goose_intel_mac.zip "${unsigned_url}"

          # begin signing
          echo "🚀 launching signing process"
          aws lambda invoke \
            --function-name codesign_helper \
            --cli-binary-format raw-in-base64-out \
            --payload "{\"source_s3_url\": \"${unsigned_url}\", \"source_job_url\": \"${source_job_url}\"}" \
            response.json > /dev/null

          if [ "$(jq -r .statusCode response.json)" != "200" ]; then
            echo "⚠️ lambda function did not return expected status code"
            exit 1
          fi

          build_number="$(jq -r .body.build_number response.json)"

          start_time=$(date +%s)

          while sleep 30; do
            aws lambda invoke \
              --function-name codesign_helper \
              --cli-binary-format raw-in-base64-out \
              --payload "{\"source_s3_url\": \"${unsigned_url}\", \"build_number\": \"${build_number}\"}" \
              response.json > /dev/null

            if [ "$(jq -r .statusCode response.json)" != "200" ]; then
              echo "⚠️ signing request returned unexpected response code $(jq -r .statusCode response.json):"
              jq . response.json
              exit 1
            fi

            state="$(jq -r .body.state response.json)"

            if [ "${state}" == "completed" ]; then
              echo "✅ signing complete ($(($(date +%s) - start_time))s)"
              break
            fi

            if [ $(($(date +%s) - start_time)) -ge 3600 ]; then
              echo "⚠️ timed out ($(($(date +%s) - start_time))s)"
              exit 1
            fi

            echo "⏲️ waiting for signing to complete (${state}: $(($(date +%s) - start_time))s)"
          done

          # parse lambda response
          signed_url=$(jq -r .body.destination_url response.json)

          # download the signed app from S3
          echo "⬇️ downloading signed app"
          aws s3 cp --quiet "${signed_url}" out/Goose-darwin-x64/Goose_intel_mac.zip
        working-directory: ui/desktop

      - name: Final cleanup before artifact upload
        run: |
          echo "Performing final cleanup..."
          # Remove build artifacts that are no longer needed
          rm -rf target || true
          # Check disk space after cleanup
          df -h

      - name: Upload Desktop artifact
        uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # pin@v4
        with:
          name: Goose-darwin-x64
          path: ui/desktop/out/Goose-darwin-x64/Goose_intel_mac.zip

      - name: Quick launch test (macOS)
        if: ${{ inputs.quick_test }}
        run: |
          # Ensure no quarantine attributes (if needed)
          xattr -cr "ui/desktop/out/Goose-darwin-x64/Goose.app"
          echo "Opening Goose.app..."
          open -g "ui/desktop/out/Goose-darwin-x64/Goose.app"

          # Give the app a few seconds to start and write logs
          sleep 5

          # Check if it's running
          if pgrep -f "Goose.app/Contents/MacOS/Goose" > /dev/null; then
            echo "App appears to be running."
          else
            echo "App did not stay open. Possible crash or startup error."
            exit 1
          fi
          # Kill the app to clean up
          pkill -f "Goose.app/Contents/MacOS/Goose"
