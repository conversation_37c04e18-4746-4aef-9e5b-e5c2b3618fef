name: Build Goose Release Candidate

on:
  pull_request:

jobs:
  bundle-desktop:
    if: startsWith(github.head_ref, 'release/')
    uses: ./.github/workflows/bundle-desktop.yml
    permissions:
      id-token: write
      contents: read

  comment-on-pr:
    needs: bundle-desktop
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Comment with download link
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # pin@v4
        with:
          issue-number: ${{ github.event.number }}
          body: |
            ### macOS ARM64 Desktop App (Apple Silicon)

            [📱 Download macOS Desktop App (arm64, signed)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/Goose-darwin-arm64.zip)

            **Instructions:**
            After downloading, unzip the file and drag the Goose.app to a location you prefer. The app is unsigned, so to run it run `xattr -r -d com.apple.quarantine '/path/to/Goose.app'` and then open the app
