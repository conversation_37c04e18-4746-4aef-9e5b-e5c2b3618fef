# This workflow is triggered by a comment on PR with the text ".build-cli"
on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number to comment on'
        required: true
        type: string

# permissions needed for reacting to IssueOps commands on PRs
permissions:
  pull-requests: write
  checks: read

name: Build CLI

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.issue && github.event.issue.number) || github.event.inputs.pr_number }}
  cancel-in-progress: true

jobs:
  trigger-on-command:
    if: >
      github.event_name == 'workflow_dispatch' ||
      (github.event.issue.pull_request && contains(github.event.comment.body, '.build-cli'))
    name: Trigger on ".build-cli" PR comment
    runs-on: ubuntu-latest
    outputs:
      continue: 'true'
      pr_number: ${{ steps.command.outputs.issue_number || github.event.inputs.pr_number }}
      head_sha: ${{ steps.set_head_sha.outputs.head_sha || github.sha }}
    steps:
      - name: Run command action
        uses: github/command@v1.3.0
        id: command
        with:
          command: ".build-cli"
          skip_reviews: true
          reaction: "eyes"
          allowed_contexts: pull_request

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
        
      - name: Get PR head SHA with gh
        id: set_head_sha
        run: |
          echo "Get PR head SHA with gh"
          HEAD_SHA=$(gh pr view "$ISSUE_NUMBER" --json headRefOid -q .headRefOid)
          echo "head_sha=$HEAD_SHA" >> $GITHUB_OUTPUT
          echo "head_sha=$HEAD_SHA"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_NUMBER: ${{ steps.command.outputs.issue_number }}

  build-cli:
    needs: [trigger-on-command]
    if: ${{ needs.trigger-on-command.outputs.continue == 'true' }}
    uses: ./.github/workflows/build-cli.yml
    with:
      ref: ${{ needs.trigger-on-command.outputs.head_sha }}

  pr-comment-cli:
    name: PR Comment with CLI builds
    runs-on: ubuntu-latest
    needs: [trigger-on-command, build-cli]
    permissions:
      pull-requests: write

    steps:
      - name: Download CLI artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: goose-*
          path: cli-dist
          merge-multiple: true

      - name: Comment on PR with CLI download links
        uses: peter-evans/create-or-update-comment@v4
        with:
          issue-number: ${{ needs.trigger-on-command.outputs.pr_number }}
          body: |
            ### CLI Builds

            Download CLI builds for different platforms:
            - [📦 Linux (x86_64)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/goose-x86_64-unknown-linux-gnu.zip)
            - [📦 Linux (aarch64)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/goose-aarch64-unknown-linux-gnu.zip)
            - [📦 macOS (x86_64)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/goose-x86_64-apple-darwin.zip)
            - [📦 macOS (aarch64)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/goose-aarch64-apple-darwin.zip)
            - [📦 Windows (x86_64)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/goose-x86_64-pc-windows-gnu.zip)

            These links are provided by nightly.link and will work even if you're not logged into GitHub.
  