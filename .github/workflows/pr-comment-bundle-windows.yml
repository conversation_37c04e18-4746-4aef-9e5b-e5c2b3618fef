# This workflow is triggered by a comment on PR with the text ".bundle-windows"
# It bundles the Windows Desktop App, then creates a PR comment with a link to download the app.

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number to comment on'
        required: true
        type: string

# permissions needed for reacting to IssueOps commands on PRs and AWS OIDC authentication
permissions:
  pull-requests: write
  checks: read
  id-token: write   # Required for AWS OIDC authentication in called workflow
  contents: read    # Required by actions/checkout in called workflow
  actions: read     # May be needed for some workflows

name: Bundle Windows Desktop App

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.issue && github.event.issue.number) || github.event.inputs.pr_number }}
  cancel-in-progress: true

jobs:
  trigger-on-command:
    if: >
      github.event_name == 'workflow_dispatch' ||
      (github.event.issue.pull_request && contains(github.event.comment.body, '.bundle-windows'))
    name: Trigger on ".bundle-windows" PR comment
    runs-on: ubuntu-latest
    outputs:
      continue: 'true'
      # Cannot use github.event.pull_request.number since the trigger is 'issue_comment'
      pr_number: ${{ steps.command.outputs.issue_number || github.event.inputs.pr_number }}
      head_sha: ${{ steps.set_head_sha.outputs.head_sha || github.sha }}
    steps:
      - name: Run command action
        uses: github/command@319d5236cc34ed2cb72a47c058a363db0b628ebe # pin@v1.3.0
        id: command
        with:
          command: ".bundle-windows"
          skip_reviews: true
          reaction: "eyes"
          allowed_contexts: pull_request

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
        
      - name: Get PR head SHA with gh
        id: set_head_sha
        run: |
          echo "Get PR head SHA with gh"
          HEAD_SHA=$(gh pr view "$ISSUE_NUMBER" --json headRefOid -q .headRefOid)
          echo "head_sha=$HEAD_SHA" >> $GITHUB_OUTPUT
          echo "head_sha=$HEAD_SHA"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_NUMBER: ${{ steps.command.outputs.issue_number }}

  bundle-desktop-windows:
    # Only run this if ".bundle-windows" command is detected.
    needs: [trigger-on-command]
    if: ${{ needs.trigger-on-command.outputs.continue == 'true' }}
    uses: ./.github/workflows/bundle-desktop-windows.yml
    with:
      signing: false
      ref: ${{ needs.trigger-on-command.outputs.head_sha }}


  pr-comment-windows:
    name: PR Comment with Windows App
    runs-on: ubuntu-latest
    needs: [trigger-on-command, bundle-desktop-windows]
    permissions:
      pull-requests: write

    steps:
      - name: Download Windows artifact
        uses: actions/download-artifact@cc203385981b70ca67e1cc392babf9cc229d5806 # pin@v4
        with:
          name: desktop-windows-dist
          path: windows-dist

      - name: Comment on PR with Windows download link
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # pin@v4
        with:
          issue-number: ${{ needs.trigger-on-command.outputs.pr_number }}
          body: |
            ### Windows Desktop App

            [🪟 Download Windows Desktop App (x64, signed)](https://nightly.link/${{ github.repository }}/actions/runs/${{ github.run_id }}/desktop-windows-dist.zip)

            **Instructions:**
            After downloading, unzip the file and run Goose.exe. The app is signed for Windows.

            This link is provided by nightly.link and will work even if you're not logged into GitHub.
