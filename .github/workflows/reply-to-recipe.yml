name: Auto-reply to <PERSON><PERSON>pe Submissions

on:
  issues:
    types: [opened]

jobs:
  thank-you-comment:
    if: contains(github.event.issue.title, '[Recipe]')
    runs-on: ubuntu-latest
    steps:
      - name: Add thank-you comment
        uses: actions/github-script@v7
        with:
          script: |
            const commentBody = [
              "🎉 Thanks for submitting your Goose recipe to the Cookbook!",
              "",
              "We appreciate you sharing your workflow with the community — our team will review your submission soon.",
              "If accepted, it’ll be added to the [Goose Recipes Cookbook](https://block.github.io/goose/recipes) and you’ll receive LLM credits as a thank-you!",
              "",
              "Stay tuned — and keep those recipes coming 🧑‍🍳🔥"
            ].join('\n');

            github.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: commentBody
            });
