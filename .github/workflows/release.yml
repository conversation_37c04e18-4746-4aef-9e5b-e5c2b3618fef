# This workflow is main release, needs to be manually tagged & pushed.
on:
  push:
    paths-ignore:
      - "documentation/**"
    tags:
      - "v1.*"

name: Release

# Permissions needed for AWS OIDC authentication in called workflows
permissions:
  id-token: write   # Required for AWS OIDC authentication in called workflow
  contents: write   # Required for creating releases and by actions/checkout
  actions: read     # May be needed for some workflows

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ------------------------------------
  # 1) Build CLI for multiple OS/Arch
  # ------------------------------------
  build-cli:
    uses: ./.github/workflows/build-cli.yml

  # ------------------------------------
  # 2) Upload Install CLI Script
  # ------------------------------------
  install-script:
    name: Upload Install Script
    runs-on: ubuntu-latest
    needs: [build-cli]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # pin@v4
      - uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # pin@v4
        with:
          name: download_cli.sh
          path: download_cli.sh

  # ------------------------------------------------------------
  # 3) Bundle Desktop App (macOS)
  # ------------------------------------------------------------
  bundle-desktop:
    uses: ./.github/workflows/bundle-desktop.yml
    permissions:
      id-token: write
      contents: read
    with:
      signing: true
    secrets:
      OSX_CODESIGN_ROLE: ${{ secrets.OSX_CODESIGN_ROLE }}

  # ------------------------------------------------------------
  # 4) Bundle Desktop App (macOS)
  # ------------------------------------------------------------
  bundle-desktop-intel:
    uses: ./.github/workflows/bundle-desktop-intel.yml
    permissions:
      id-token: write
      contents: read
    with:
      signing: true
    secrets:
      OSX_CODESIGN_ROLE: ${{ secrets.OSX_CODESIGN_ROLE }}

  # ------------------------------------------------------------
  # 5) Bundle Desktop App (Linux)
  # ------------------------------------------------------------
  bundle-desktop-linux:
    uses: ./.github/workflows/bundle-desktop-linux.yml

#  # ------------------------------------------------------------
#  # 6) Bundle Desktop App (Windows)
#  # ------------------------------------------------------------
  bundle-desktop-windows:
    uses: ./.github/workflows/bundle-desktop-windows.yml
    with:
      signing: true
    secrets:
      WINDOWS_CODESIGN_CERTIFICATE: ${{ secrets.WINDOWS_CODESIGN_CERTIFICATE }}
      WINDOW_SIGNING_ROLE: ${{ secrets.WINDOW_SIGNING_ROLE }}
      WINDOW_SIGNING_ROLE_TAG: ${{ secrets.WINDOW_SIGNING_ROLE_TAG }}

  # ------------------------------------
  # 7) Create/Update GitHub Release
  # ------------------------------------
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [build-cli, install-script, bundle-desktop, bundle-desktop-intel, bundle-desktop-linux, bundle-desktop-windows]
    permissions:
      contents: write
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@cc203385981b70ca67e1cc392babf9cc229d5806 # pin@v4
        with:
          merge-multiple: true

      # Create/update the versioned release
      - name: Release versioned
        uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # pin@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          artifacts: |
            goose-*.tar.bz2
            goose-*.zip
            Goose*.zip
            *.deb
            *.rpm
            download_cli.sh
          allowUpdates: true
          omitBody: true
          omitPrereleaseDuringUpdate: true

      # Create/update the stable release
      - name: Release stable
        uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # pin@v1
        with:
          tag: stable
          name: Stable
          token: ${{ secrets.GITHUB_TOKEN }}
          artifacts: |
            goose-*.tar.bz2
            goose-*.zip
            Goose*.zip
            *.deb
            *.rpm
            download_cli.sh
          allowUpdates: true
          omitBody: true
          omitPrereleaseDuringUpdate: true
