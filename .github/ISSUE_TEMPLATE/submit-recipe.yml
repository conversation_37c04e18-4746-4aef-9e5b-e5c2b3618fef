name: 🧑‍🍳 Submit a Recipe to the Goose Cookbook
description: Share a reusable Goose recipe with the community!
title: "[<PERSON>ci<PERSON>] <your recipe title here>"
labels: ["recipe submission"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for contributing to the Goose Cookbook! 🍳  
        Recipes are reusable sessions created in Goose Desktop or CLI and shared with the community to help others vibe code faster.

        📌 **How to Submit**
        - Create your recipe using <PERSON> ("Make recipe from this session")
        - Fill out the YAML below using the format provided
        - Paste it into the field and submit the issue — we'll review and add it to the Cookbook!

        🪄 **What Happens After?**
        - If accepted, we'll publish your recipe to the [Goose Recipes Cookbook](https://block.github.io/goose/recipes)
        - You'll receive OpenRouter **LLM API credits** as a thank you!
        - Your GitHub handle will be displayed and linked on the recipe card
        - If you provide an email below, we'll email you your credits when your recipe is approved and merged.
        - If the YAML has any issues, <PERSON> will comment with validation errors so you can fix and resubmit.

        🧪 **Pro Tip:** You can test your recipe locally in your terminal with:  
        `goose recipe validate your-recipe.yaml`

  - type: textarea
    id: recipe-yaml
    attributes:
      label: Paste Your Full Recipe YAML Below
      description: Use the structure below and we’ll auto-fill your GitHub handle for `author.contact` after submission.
      placeholder: |
        version: "1.0.0"
        id: clean-up-feature-flag
        title: Clean Up Feature Flag
        description: Automatically clean up all references of a fully rolled out feature flag from a codebase and make the new behavior the default.
        instructions: |
          Your job is to systematically remove a fully rolled out feature flag and ensure the new behavior is now the default. Use code search tools like ripgrep to identify all references to the flag, clean up definition files, usage sites, tests, and configuration files. Then create a commit and push changes with clear commit messages documenting the flag removal.
        prompt: |
          Task: Remove a feature flag that has been fully rolled out, where the feature flag's functionality should become the default behavior.

          Context:
            Feature flag key: {{ feature_flag_key }}
            Project: {{ repo_dir }}

          Steps to follow:
            1. Check out a *new* branch from main or master named using the feature flag key.
            2. Find the feature flag constant/object that wraps the key.
            3. Search for all references to the constant/object using ripgrep or equivalent tools.
            4. Remove all conditional logic and make the new behavior default.
            5. Remove unused imports, mocks, config, and tests.
            6. Commit your changes and push the branch.
            7. Open a GitHub PR.

          Use commit messages like:
            chore(flag-cleanup): remove <feature_flag_key> flag from codebase

        parameters:
          - key: feature_flag_key
            input_type: string
            requirement: required
            description: Key of the feature flag

          - key: repo_dir
            input_type: string
            requirement: optional
            default: ./
            description: Directory of the codebase

        extensions:
          - type: stdio
            name: developer
            cmd: uvx
            args:
              - developer-mcp@latest
            timeout: 300
            bundled: true
            description: Access developer tools
        
        activities:
          - Remove feature flag definitions
          - Clean up feature flag usage sites
          - Update affected tests
          - Remove flag configurations
          - Document flag removal
    validations:
      required: true

  - type: input
    id: email
    attributes:
      label: Your Email (optional)
      description: If your recipe is approved, we'll email your LLM API credits here.
      placeholder: <EMAIL>
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        🛠 **Recipe Field Tips**
        - `version` must be "1.0.0" for now
        - `id` should be lowercase, hyphenated, and unique (e.g. `my-awesome-recipe`)
        - `title` is the display name of your recipe
        - `description` should clearly explain what the recipe does
        - `instructions` are specific steps Goose should follow — supports template variables like `{{ variable_name }}`
        - `prompt` is the first thing Goose sees when the recipe is launched
        - `parameters` should include required or optional inputs — optional ones must have `default`
        - `extensions` must follow the full format with `type`, `cmd`, `args`, `timeout`, etc.
        - `activities` describe the main actions the recipe performs
