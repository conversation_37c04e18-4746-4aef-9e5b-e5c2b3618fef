{"version": "2.0.0", "tasks": [{"label": "llama:build", "type": "shell", "command": "bash -lc 'mkdir -p build && cd build && cmake -DLLAMA_METAL=1 .. && cmake --build . -j'", "options": {"cwd": "$HOME/llama.cpp"}, "problemMatcher": []}, {"label": "llama:serve", "type": "shell", "command": "bash -lc 'MODEL=\"$HOME/.lmstudio/models/lmstudio-community/Qwen3-8B-GGUF/Qwen3-8B-Q4_K_M.gguf\"; \"$HOME/llama.cpp/build/bin/llama-server\" -m \"$MODEL\" -c 8192 -ngl 999 --port 8081 --alias qwen-local'", "options": {"cwd": "$HOME/llama.cpp"}, "isBackground": true, "problemMatcher": []}, {"label": "goose:openai", "type": "shell", "command": "bash -lc 'source ./bin/activate-hermit && export OPENAI_API_KEY=llama OPENAI_HOST=http://127.0.0.1:8081 OPENAI_BASE_PATH=/v1 GOOSE_PROVIDER__TYPE=openai GOOSE_PROVIDER__HOST=http://127.0.0.1:8081 GOOSE_PROVIDER__MODEL=qwen-local; cargo run -p goose-cli --release -- session'", "options": {"cwd": "${workspaceFolder}"}, "presentation": {"panel": "dedicated"}, "problemMatcher": []}]}