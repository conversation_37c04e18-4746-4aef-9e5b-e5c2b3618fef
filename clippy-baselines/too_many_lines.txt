crates/goose-bench/src/eval_suites/core/developer/simple_repo_clone_test.rs::run
crates/goose-cli/src/cli.rs::cli
crates/goose-cli/src/commands/configure.rs::configure_extensions_dialog
crates/goose-cli/src/commands/configure.rs::configure_provider_dialog
crates/goose-cli/src/commands/configure.rs::configure_tool_permissions_dialog
crates/goose-cli/src/commands/configure.rs::handle_configure
crates/goose-cli/src/commands/project.rs::handle_project_default
crates/goose-cli/src/commands/project.rs::handle_projects_interactive
crates/goose-cli/src/commands/web.rs::handle_socket
crates/goose-cli/src/commands/web.rs::process_message_streaming
crates/goose-cli/src/session/builder.rs::build_session
crates/goose-cli/src/session/export.rs::tool_response_to_markdown
crates/goose-cli/src/session/mod.rs::handle_interrupted_messages
crates/goose-cli/src/session/mod.rs::interactive
crates/goose-cli/src/session/mod.rs::process_agent_response
crates/goose-mcp/src/computercontroller/docx_tool.rs::docx_tool
crates/goose-mcp/src/computercontroller/mod.rs::new
crates/goose-mcp/src/computercontroller/mod.rs::quick_script
crates/goose-mcp/src/computercontroller/mod.rs::xlsx_tool
crates/goose-mcp/src/computercontroller/pdf_tool.rs::pdf_tool
crates/goose-mcp/src/developer/mod.rs::bash
crates/goose-mcp/src/developer/mod.rs::new
crates/goose-mcp/src/google_drive/google_labels.rs::doit
crates/goose-mcp/src/google_drive/mod.rs::create_file
crates/goose-mcp/src/google_drive/mod.rs::docs_tool
crates/goose-mcp/src/google_drive/mod.rs::new
crates/goose-mcp/src/google_drive/mod.rs::search_files
crates/goose-mcp/src/google_drive/mod.rs::sharing
crates/goose-mcp/src/google_drive/mod.rs::sheets_tool
crates/goose-mcp/src/google_drive/mod.rs::update_label
crates/goose-mcp/src/memory/mod.rs::new
crates/goose-server/src/openapi.rs::convert_typed_schema
crates/goose-server/src/openapi.rs::convert_typed_schema
crates/goose-server/src/routes/audio.rs::transcribe_elevenlabs_handler
crates/goose-server/src/routes/audio.rs::transcribe_elevenlabs_handler
crates/goose-server/src/routes/extension.rs::add_extension
crates/goose-server/src/routes/extension.rs::add_extension
crates/goose-server/src/routes/extension.rs::is_command_allowed_with_allowlist
crates/goose-server/src/routes/extension.rs::is_command_allowed_with_allowlist
crates/goose-server/src/routes/reply.rs::reply_handler
crates/goose-server/src/routes/reply.rs::reply_handler
crates/goose/src/agents/agent.rs::create_recipe
crates/goose/src/agents/agent.rs::reply_internal
crates/goose/src/agents/extension_manager.rs::add_extension
crates/goose/src/providers/azure.rs::post
crates/goose/src/providers/databricks.rs::post_with_retry
crates/goose/src/providers/formats/anthropic.rs::format_messages
crates/goose/src/providers/formats/anthropic.rs::response_to_streaming_message<S>
crates/goose/src/providers/formats/databricks.rs::format_messages
crates/goose/src/providers/formats/google.rs::format_messages
crates/goose/src/providers/formats/openai.rs::format_messages
crates/goose/src/providers/formats/openai.rs::response_to_streaming_message<S>
crates/goose/src/providers/gcpvertexai.rs::post_with_location
crates/goose/src/providers/snowflake.rs::post
crates/goose/src/scheduler.rs::add_scheduled_job
crates/goose/src/scheduler.rs::load_jobs_from_storage
crates/goose/src/scheduler.rs::run_scheduled_job_internal
crates/goose/src/scheduler.rs::update_schedule
crates/goose/src/session/storage.rs::read_messages_with_truncation
crates/mcp-server/src/lib.rs::run<R, W>
