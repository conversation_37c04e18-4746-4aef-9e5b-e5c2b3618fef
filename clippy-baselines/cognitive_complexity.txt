crates/goose-bench/src/runners/eval_runner.rs::run<F, Fut>
crates/goose-bench/src/runners/metric_aggregator.rs::generate_csv_from_benchmark_dir
crates/goose-bench/src/utilities.rs::await_process_exits
crates/goose-cli/src/cli.rs::cli
crates/goose-cli/src/commands/web.rs::handle_socket
crates/goose-cli/src/commands/web.rs::process_message_streaming
crates/goose-cli/src/session/builder.rs::build_session
crates/goose-cli/src/session/mod.rs::interactive
crates/goose-cli/src/session/mod.rs::process_agent_response
crates/goose-mcp/src/computercontroller/docx_tool.rs::docx_tool
crates/goose-mcp/src/computercontroller/pdf_tool.rs::pdf_tool
crates/goose-mcp/src/google_drive/mod.rs::docs_tool
crates/goose-mcp/src/google_drive/mod.rs::google_auth
crates/goose-mcp/src/google_drive/oauth_pkce.rs::perform_oauth_flow
crates/goose-mcp/src/google_drive/storage.rs::write_to_file
crates/goose-server/src/commands/agent.rs::run
crates/goose-server/src/openapi.rs::convert_typed_schema
crates/goose-server/src/openapi.rs::convert_typed_schema
crates/goose-server/src/routes/audio.rs::transcribe_elevenlabs_handler
crates/goose-server/src/routes/audio.rs::transcribe_elevenlabs_handler
crates/goose-server/src/routes/audio.rs::transcribe_handler
crates/goose-server/src/routes/audio.rs::transcribe_handler
crates/goose-server/src/routes/config_management.rs::get_pricing
crates/goose-server/src/routes/config_management.rs::get_pricing
crates/goose-server/src/routes/extension.rs::add_extension
crates/goose-server/src/routes/extension.rs::add_extension
crates/goose-server/src/routes/session.rs::get_session_insights
crates/goose-server/src/routes/session.rs::get_session_insights
crates/goose-server/src/routes/setup.rs::start_openrouter_setup
crates/goose-server/src/routes/setup.rs::start_openrouter_setup
crates/goose/src/agents/extension_manager.rs::merge_environments
crates/goose/src/agents/retry.rs::execute_on_failure_command
crates/goose/src/agents/retry.rs::handle_retry_logic
crates/goose/src/config/base.rs::try_restore_from_backup
crates/goose/src/context_mgmt/truncate.rs::truncate_messages
crates/goose/src/providers/anthropic.rs::post
crates/goose/src/providers/azure.rs::post
crates/goose/src/providers/claude_code.rs::find_claude_executable
crates/goose/src/providers/databricks.rs::post_with_retry
crates/goose/src/providers/gcpvertexai.rs::post_with_location
crates/goose/src/providers/gemini_cli.rs::find_gemini_executable
crates/goose/src/providers/githubcopilot.rs::poll_for_access_token
crates/goose/src/providers/lead_worker.rs::detect_task_failures
crates/goose/src/providers/lead_worker.rs::handle_completion_result
crates/goose/src/providers/oauth.rs::get_oauth_token_async
crates/goose/src/providers/pricing.rs::load_from_disk
crates/goose/src/providers/snowflake.rs::post
crates/goose/src/providers/venice.rs::post
crates/goose/src/providers/xai.rs::post
crates/goose/src/scheduler_factory.rs::create
crates/goose/src/scheduler_factory.rs::from_config
crates/goose/src/scheduler.rs::add_scheduled_job
crates/goose/src/scheduler.rs::kill_running_job
crates/goose/src/scheduler.rs::load_jobs_from_storage
crates/goose/src/scheduler.rs::run_scheduled_job_internal
crates/goose/src/session/storage.rs::parse_message_with_truncation
crates/goose/src/session/storage.rs::read_messages_with_truncation
crates/goose/src/temporal_scheduler.rs::discover_http_port
crates/goose/src/temporal_scheduler.rs::find_go_service_binary
crates/goose/src/temporal_scheduler.rs::new
crates/goose/src/temporal_scheduler.rs::start_go_service
crates/goose/src/temporal_scheduler.rs::stop_services
crates/goose/src/temporal_scheduler.rs::update_job_status_from_sessions
crates/goose/src/temporal_scheduler.rs::wait_for_service_ready
crates/mcp-client/src/oauth.rs::execute
crates/mcp-client/src/oauth.rs::get_oauth_endpoints
crates/mcp-server/src/lib.rs::run<R, W>
